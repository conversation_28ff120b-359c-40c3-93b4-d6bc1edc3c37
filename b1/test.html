<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>房产评价系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        body {
            background-color: #f5f5f5;
            color: #333;
            overflow-x: hidden;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: #fff;
            position: relative;
            overflow: hidden;
            height: 100vh;
        }
        .slide-container {
            width: 100%;
            transition: transform 0.3s ease-out;
            touch-action: pan-y;
        }
        .slide {
            width: 100%;
            min-height: 100vh;
            padding-bottom: 60px; /* 为底部按钮留出空间 */
        }
        .rating-item {
            display: flex;
            padding: 15px;
            border-bottom: 1px solid #eee;
            align-items: center;
        }
        .rating-label {
            width: 80px;
            font-size: 16px;
            color: #333;
        }
        .stars {
            display: flex;
        }
        .star {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" fill="%23e0e0e0"/></svg>');
            background-size: cover;
            cursor: pointer;
        }
        .star.active {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" fill="%23ff9800"/></svg>');
        }
        .comment-area {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }
        .comment-label {
            font-size: 16px;
            margin-bottom: 10px;
            color: #333;
        }
        .comment-input {
            width: 100%;
            height: 100px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-size: 14px;
            resize: none;
        }
        .char-count {
            text-align: right;
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }
        .upload-area {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }
        .upload-button {
            width: 80px;
            height: 80px;
            border: 1px dashed #ddd;
            border-radius: 4px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            background: #fafafa;
        }
        .upload-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 5px;
        }
        .upload-text {
            font-size: 12px;
            color: #999;
        }
        .preview-image {
            max-width: 100%;
            max-height: 200px;
            margin-top: 10px;
            display: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .user-type {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        .radio-container {
            display: flex;
            align-items: center;
        }
        .radio-label {
            margin-left: 5px;
            font-size: 14px;
            color: #333;
        }
        .hint {
            margin-left: 15px;
            font-size: 12px;
            color: #ff6e00;
        }
        .terms {
            padding: 15px;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        .terms a {
            color: #1890ff;
            text-decoration: none;
        }
        .submit-button {
            display: block;
            width: 100%;
            padding: 15px 0;
            background-color: #ff6e00;
            color: white;
            border: none;
            font-size: 16px;
            cursor: pointer;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            max-width: 600px;
            margin: 0 auto;
            z-index: 10;
        }
        .slide-indicator {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            z-index: 10;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .slide-indicator.visible {
            opacity: 1;
        }
        /* 图片预览模态框样式 */
        .image-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            z-index: 100;
            justify-content: center;
            align-items: center;
        }
        .modal-content {
            max-width: 90%;
            max-height: 90%;
            position: relative;
        }
        .modal-image {
            width: 100%;
            height: auto;
            border-radius: 4px;
        }
        .close-modal {
            position: absolute;
            top: -30px;
            right: 0;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }
        .page-indicator {
            position: fixed;
            bottom: 70px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            z-index: 10;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="slide-indicator">滑动查看更多</div>
        <div class="slide-container">
            <div class="slide" id="slide1">
                <div class="rating-item">
                    <div class="rating-label">地 段</div>
                    <div class="stars" data-category="location">
                        <div class="star" data-value="1"></div>
                        <div class="star" data-value="2"></div>
                        <div class="star" data-value="3"></div>
                        <div class="star" data-value="4"></div>
                        <div class="star" data-value="5"></div>
                    </div>
                </div>
                
                <div class="rating-item">
                    <div class="rating-label">交 通</div>
                    <div class="stars" data-category="transportation">
                        <div class="star" data-value="1"></div>
                        <div class="star" data-value="2"></div>
                        <div class="star" data-value="3"></div>
                        <div class="star" data-value="4"></div>
                        <div class="star" data-value="5"></div>
                    </div>
                </div>
                
                <div class="rating-item">
                    <div class="rating-label">配 套</div>
                    <div class="stars" data-category="facilities">
                        <div class="star" data-value="1"></div>
                        <div class="star" data-value="2"></div>
                        <div class="star" data-value="3"></div>
                        <div class="star" data-value="4"></div>
                        <div class="star" data-value="5"></div>
                    </div>
                </div>
                
                <div class="rating-item">
                    <div class="rating-label">环 境</div>
                    <div class="stars" data-category="environment">
                        <div class="star" data-value="1"></div>
                        <div class="star" data-value="2"></div>
                        <div class="star" data-value="3"></div>
                        <div class="star" data-value="4"></div>
                        <div class="star" data-value="5"></div>
                    </div>
                </div>
                
                <div class="rating-item">
                    <div class="rating-label">性价比</div>
                    <div class="stars" data-category="value">
                        <div class="star" data-value="1"></div>
                        <div class="star" data-value="2"></div>
                        <div class="star" data-value="3"></div>
                        <div class="star" data-value="4"></div>
                        <div class="star" data-value="5"></div>
                    </div>
                </div>
            </div>
            
            <div class="slide" id="slide2">
                <div class="comment-area">
                    <div class="comment-label">说说你对楼盘的印象吧</div>
                    <textarea class="comment-input" id="commentInput" maxlength="200" placeholder=""></textarea>
                    <div class="char-count">还差<span id="charCount">10</span>个字</div>
                </div>
                
                <div class="upload-area">
                    <div class="upload-button" id="uploadButton">
                        <svg class="upload-icon" viewBox="0 0 24 24" fill="#999">
                            <path d="M19 7v11H5V7H3v12c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V7h-2z"/>
                            <path d="M16 7l-5-5-5 5h3v8h4V7h3z"/>
                        </svg>
                        <div class="upload-text">上传照片</div>
                    </div>
                    <img id="previewImage" class="preview-image" src="" alt="预览图片">
                    <input type="file" id="fileInput" accept="image/*" style="display: none;">
                </div>
            </div>
            
            <div class="slide" id="slide3">
                <div class="user-type">
                    <div class="radio-container">
                        <input type="radio" id="ownerRadio" name="userType" value="owner">
                        <label class="radio-label" for="ownerRadio">我是业主</label>
                    </div>
                    <div class="hint">业主填写购房信息有神马好处?</div>
                </div>
                
                <div class="terms">
                    <a href="#">《房小二网用户点评内容管理规范》</a>
                </div>
            </div>
        </div>
        
        <div class="page-indicator">第 <span id="currentPage">1</span> / <span id="totalPages">3</span> 页</div>
        <button class="submit-button" id="submitButton">发布</button>
    </div>

    <!-- 图片预览模态框 -->
    <div class="image-modal" id="imageModal">
        <div class="modal-content">
            <span class="close-modal" id="closeModal">&times;</span>
            <img class="modal-image" id="modalImage" src="" alt="预览大图">
        </div>
    </div>

    <script>
        // 滑动功能
        const slideContainer = document.querySelector('.slide-container');
        const slides = document.querySelectorAll('.slide');
        const slideIndicator = document.querySelector('.slide-indicator');
        const currentPageEl = document.getElementById('currentPage');
        const totalPagesEl = document.getElementById('totalPages');
        let currentSlide = 0;
        let startY = 0;
        let startTranslate = 0;
        let containerHeight = 0;
        
        // 初始化
        function initSlider() {
            containerHeight = document.querySelector('.container').offsetHeight;
            updateSlidePosition();
            totalPagesEl.textContent = slides.length;
        }
        
        // 更新滑动位置
        function updateSlidePosition(animate = true) {
            if (animate) {
                slideContainer.style.transition = 'transform 0.3s ease-out';
            } else {
                slideContainer.style.transition = 'none';
            }
            slideContainer.style.transform = `translateY(-${currentSlide * containerHeight}px)`;
            currentPageEl.textContent = currentSlide + 1;
        }
        
        // 触摸开始事件
        slideContainer.addEventListener('touchstart', (e) => {
            startY = e.touches[0].clientY;
            startTranslate = -currentSlide * containerHeight;
            slideContainer.style.transition = 'none';
        });
        
        // 触摸移动事件
        slideContainer.addEventListener('touchmove', (e) => {
            const currentY = e.touches[0].clientY;
            const diff = currentY - startY;
            const newTranslate = startTranslate + diff;
            
            // 限制滑动范围
            if (newTranslate > 0 || newTranslate < -(slides.length - 1) * containerHeight) {
                return;
            }
            
            slideContainer.style.transform = `translateY(${newTranslate}px)`;
            
            // 显示滑动指示器
            slideIndicator.classList.add('visible');
            slideIndicator.textContent = diff > 0 ? '↑ 上滑查看上一页' : '↓ 下滑查看下一页';
        });
        
        // 触摸结束事件
        slideContainer.addEventListener('touchend', (e) => {
            const currentY = e.changedTouches[0].clientY;
            const diff = currentY - startY;
            
            // 隐藏滑动指示器
            slideIndicator.classList.remove('visible');
            
            // 判断滑动方向和距离
            if (Math.abs(diff) > 50) {
                if (diff > 0 && currentSlide > 0) {
                    // 向上滑动，显示上一页
                    currentSlide--;
                } else if (diff < 0 && currentSlide < slides.length - 1) {
                    // 向下滑动，显示下一页
                    currentSlide++;
                }
            }
            
            updateSlidePosition();
        });
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            containerHeight = document.querySelector('.container').offsetHeight;
            updateSlidePosition(false);
        });
        
        // 初始化滑动功能
        window.addEventListener('load', initSlider);
        
        // 星星评分功能
        document.querySelectorAll('.stars').forEach(starsContainer => {
            const stars = starsContainer.querySelectorAll('.star');
            let selectedRating = 0;
            
            stars.forEach(star => {
                star.addEventListener('click', function() {
                    const value = parseInt(this.getAttribute('data-value'));
                    selectedRating = value;
                    
                    // 更新星星显示
                    stars.forEach(s => {
                        const starValue = parseInt(s.getAttribute('data-value'));
                        if (starValue <= selectedRating) {
                            s.classList.add('active');
                        } else {
                            s.classList.remove('active');
                        }
                    });
                });
            });
        });
        
        // 字数统计功能
        const commentInput = document.getElementById('commentInput');
        const charCount = document.getElementById('charCount');
        
        commentInput.addEventListener('input', function() {
            const remainingChars = Math.max(10 - this.value.length, 0);
            charCount.textContent = remainingChars;
        });
        
        // 图片上传功能
        const uploadButton = document.getElementById('uploadButton');
        const fileInput = document.getElementById('fileInput');
        const previewImage = document.getElementById('previewImage');
        const imageModal = document.getElementById('imageModal');
        const modalImage = document.getElementById('modalImage');
        const closeModal = document.getElementById('closeModal');
        
        uploadButton.addEventListener('click', function() {
            fileInput.click();
        });
        
        fileInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    previewImage.src = e.target.result;
                    previewImage.style.display = 'block';
                };
                
                reader.readAsDataURL(this.files[0]);
            }
        });
        
        // 图片预览功能
        previewImage.addEventListener('click', function() {
            modalImage.src = this.src;
            imageModal.style.display = 'flex';
            document.body.style.overflow = 'hidden'; // 防止背景滚动
        });
        
        closeModal.addEventListener('click', function() {
            imageModal.style.display = 'none';
            document.body.style.overflow = ''; // 恢复滚动
        });
        
        imageModal.addEventListener('click', function(e) {
            if (e.target === this) {
                imageModal.style.display = 'none';
                document.body.style.overflow = ''; // 恢复滚动
            }
        });
        
        // 提交按钮功能
        const submitButton = document.getElementById('submitButton');
        
        submitButton.addEventListener('click', function() {
            // 收集所有评分数据
            const ratings = {};
            document.querySelectorAll('.stars').forEach(starsContainer => {
                const category = starsContainer.getAttribute('data-category');
                const activeStars = starsContainer.querySelectorAll('.star.active').length;
                ratings[category] = activeStars;
            });
            
            // 获取评论内容
            const comment = commentInput.value;
            
            // 获取用户类型
            const isOwner = document.getElementById('ownerRadio').checked;
            
            // 获取图片（如果有）
            const hasImage = previewImage.style.display === 'block';
            
            // 验证必填项
            if (comment.length < 10) {
                alert('评论至少需要10个字');
                return;
            }
            
            // 这里可以添加提交到服务器的代码
            console.log('提交数据:', {
                ratings,
                comment,
                isOwner,
                hasImage
            });
            
            alert('评价提交成功！');
        });
    </script>
</body>
</html>